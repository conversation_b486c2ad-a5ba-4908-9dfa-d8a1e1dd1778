import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import slugify from "slugify"
import readingTime from "reading-time"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Generate a URL-friendly slug from a title
 */
export function generateSlug(title: string): string {
  return slugify(title, {
    lower: true,
    strict: true,
    remove: /[*+~.()'"!:@]/g,
  })
}

/**
 * Calculate reading time from content (in minutes)
 */
export function calculateReadingTime(content: string): number {
  const stats = readingTime(content)
  return Math.ceil(stats.minutes)
}

/**
 * Strip HTML tags from content for reading time calculation
 */
export function stripHtml(html: string): string {
  return html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim()
}

/**
 * Extract plain text from rich text content for reading time calculation
 */
export function extractTextFromRichContent(content: string): string {
  // If content is JSON (TipTap format), extract text
  try {
    const parsed = JSON.parse(content)
    if (parsed.type === 'doc' && parsed.content) {
      return extractTextFromNodes(parsed.content)
    }
  } catch {
    // If not JSON, treat as HTML/markdown and strip tags
    return stripHtml(content)
  }

  return stripHtml(content)
}

/**
 * Recursively extract text from TipTap nodes
 */
interface TipTapNode {
  type: string;
  text?: string;
  content?: TipTapNode[];
}

function extractTextFromNodes(nodes: TipTapNode[]): string {
  let text = ''

  for (const node of nodes) {
    if (node.type === 'text') {
      text += node.text || ''
    } else if (node.content) {
      text += extractTextFromNodes(node.content)
    }

    // Add space after block elements
    if (['paragraph', 'heading', 'listItem'].includes(node.type)) {
      text += ' '
    }
  }

  return text.trim()
}

/**
 * Validate and sanitize slug
 */
export function validateSlug(slug: string): string {
  if (!slug || slug.trim() === '') {
    throw new Error('Slug cannot be empty')
  }

  const sanitized = slugify(slug, {
    lower: true,
    strict: true,
    remove: /[*+~.()'"!:@]/g,
  })

  if (sanitized !== slug) {
    return sanitized
  }

  return slug
}
