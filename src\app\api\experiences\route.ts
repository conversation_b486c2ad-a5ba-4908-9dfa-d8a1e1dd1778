import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { withCors, handleOptions } from '@/lib/cors'

export async function GET() {
  try {
    const experiences = await prisma.experience.findMany({
      where: {
        published: true,
      },
      orderBy: {
        order: 'asc',
      },
    })

    return withCors(NextResponse.json(experiences))
  } catch (error) {
    console.error('Error fetching experiences:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to fetch experiences' },
      { status: 500 }
    ))
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      title,
      company,
      companyLogo,
      location,
      period,
      type,
      description,
      achievements,
      technologies,
      website,
      order,
      published,
    } = body

    const experience = await prisma.experience.create({
      data: {
        title,
        company,
        companyLogo,
        location,
        period,
        type,
        description,
        achievements: achievements || [],
        technologies: technologies || [],
        website,
        order: order || 0,
        published: published !== undefined ? published : true,
      },
    })

    return withCors(NextResponse.json(experience, { status: 201 }))
  } catch (error) {
    console.error('Error creating experience:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to create experience' },
      { status: 500 }
    ))
  }
}

export async function OPTIONS() {
  return handleOptions()
}
