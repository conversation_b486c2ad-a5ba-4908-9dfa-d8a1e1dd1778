import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedExperienceEducation() {
  console.log('Seeding experience and education data...')

  // Create experiences
  const experiences = [
    {
      title: "Senior Full Stack Developer",
      company: "TechCorp Solutions",
      companyLogo: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop&crop=center",
      location: "Mumbai, India",
      period: "2022 - Present",
      type: "Full-time",
      description: "Leading development of scalable web applications using React, Next.js, and Node.js. Mentoring junior developers and implementing best practices for code quality and performance.",
      achievements: [
        "Increased application performance by 40% through optimization",
        "Led a team of 5 developers on multiple projects",
        "Implemented CI/CD pipelines reducing deployment time by 60%",
        "Architected microservices handling 1M+ requests daily"
      ],
      technologies: ["React", "Next.js", "TypeScript", "Node.js", "PostgreSQL", "AWS"],
      website: "https://techcorp.com",
      order: 0,
      published: true
    },
    {
      title: "Full Stack Developer",
      company: "InnovateTech",
      companyLogo: "https://images.unsplash.com/photo-1549923746-c502d488b3ea?w=100&h=100&fit=crop&crop=center",
      location: "Bangalore, India",
      period: "2021 - 2022",
      type: "Full-time",
      description: "Developed and maintained multiple client projects using modern web technologies. Collaborated with cross-functional teams to deliver high-quality software solutions.",
      achievements: [
        "Built 10+ responsive web applications",
        "Improved code quality through comprehensive testing",
        "Reduced bug reports by 50% through better QA processes",
        "Mentored 2 junior developers"
      ],
      technologies: ["React", "Vue.js", "Node.js", "Express", "MongoDB", "Docker"],
      website: "https://innovatetech.com",
      order: 1,
      published: true
    },
    {
      title: "Frontend Developer",
      company: "WebCraft Studios",
      companyLogo: "https://images.unsplash.com/photo-1572021335469-31706a17aaef?w=100&h=100&fit=crop&crop=center",
      location: "Delhi, India",
      period: "2020 - 2021",
      type: "Full-time",
      description: "Specialized in creating beautiful and responsive user interfaces. Worked closely with designers to implement pixel-perfect designs and ensure excellent user experience.",
      achievements: [
        "Delivered 20+ pixel-perfect UI implementations",
        "Improved website loading speed by 35%",
        "Implemented responsive designs for mobile-first approach",
        "Collaborated with UX team on user research"
      ],
      technologies: ["HTML", "CSS", "JavaScript", "React", "Sass", "Webpack"],
      website: "https://webcraftstudios.com",
      order: 2,
      published: true
    }
  ]

  for (const exp of experiences) {
    await prisma.experience.create({
      data: exp
    })
  }

  // Create education
  const education = [
    {
      degree: "Bachelor of Engineering in Computer Science",
      institution: "Chandigarh University",
      location: "Chandigarh, India",
      period: "2020 - 2024",
      grade: "First Class with Distinction (8.5/10 CGPA)",
      description: "Focused on software engineering, data structures, algorithms, and web technologies. Completed final year project on e-commerce platform development.",
      highlights: [
        "Dean's List for 3 consecutive semesters",
        "Led university coding club with 200+ members",
        "Won inter-college hackathon for innovative web solution",
        "Published research paper on web performance optimization"
      ],
      order: 0,
      published: true
    },
    {
      degree: "Higher Secondary Certificate (Science)",
      institution: "St. Xavier's College",
      location: "Mumbai, India",
      period: "2014 - 2016",
      grade: "92.5%",
      description: "Specialized in Mathematics, Physics, and Chemistry with additional focus on computer science fundamentals.",
      highlights: [
        "School topper in Computer Science",
        "Represented school in state-level science exhibition",
        "Active member of robotics club",
        "Achieved distinction in mathematics"
      ],
      order: 1,
      published: true
    }
  ]

  for (const edu of education) {
    await prisma.education.create({
      data: edu
    })
  }

  // Create certifications
  const certifications = [
    {
      title: "AWS Certified Solutions Architect",
      issuer: "Amazon Web Services",
      date: "2023",
      credentialId: "AWS-CSA-2023-001",
      emoji: "☁️",
      description: "Comprehensive certification covering AWS architecture best practices, security, and scalability.",
      order: 0,
      published: true
    },
    {
      title: "React Developer Certification",
      issuer: "Meta (Facebook)",
      date: "2022",
      credentialId: "META-REACT-2022-456",
      emoji: "⚛️",
      description: "Advanced React development patterns, hooks, and modern React ecosystem.",
      order: 1,
      published: true
    },
    {
      title: "Google Cloud Professional Developer",
      issuer: "Google Cloud",
      date: "2023",
      credentialId: "GCP-DEV-2023-789",
      emoji: "🌐",
      description: "Expertise in developing scalable applications on Google Cloud Platform.",
      order: 2,
      published: true
    },
    {
      title: "MongoDB Certified Developer",
      issuer: "MongoDB Inc.",
      date: "2021",
      credentialId: "MONGO-DEV-2021-123",
      emoji: "🍃",
      description: "Proficiency in MongoDB database design, development, and optimization.",
      order: 3,
      published: true
    }
  ]

  for (const cert of certifications) {
    await prisma.certification.create({
      data: cert
    })
  }

  console.log('Experience and education data seeded successfully!')
}

seedExperienceEducation()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
