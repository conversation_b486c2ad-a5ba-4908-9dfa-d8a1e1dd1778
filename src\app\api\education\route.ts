import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { withCors, handleOptions } from '@/lib/cors'

export async function GET() {
  try {
    const education = await prisma.education.findMany({
      where: {
        published: true,
      },
      orderBy: {
        order: 'asc',
      },
    })

    return withCors(NextResponse.json(education))
  } catch (error) {
    console.error('Error fetching education:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to fetch education' },
      { status: 500 }
    ))
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      degree,
      institution,
      location,
      period,
      grade,
      description,
      highlights,
      order,
      published,
    } = body

    const education = await prisma.education.create({
      data: {
        degree,
        institution,
        location,
        period,
        grade,
        description,
        highlights: highlights || [],
        order: order || 0,
        published: published !== undefined ? published : true,
      },
    })

    return withCors(NextResponse.json(education, { status: 201 }))
  } catch (error) {
    console.error('Error creating education:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to create education' },
      { status: 500 }
    ))
  }
}

export async function OPTIONS() {
  return handleOptions()
}
