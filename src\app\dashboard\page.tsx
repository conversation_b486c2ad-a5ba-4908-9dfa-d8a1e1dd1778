'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { DashboardLayout } from '@/components/dashboard/layout'
import {
  FolderOpen,
  FileText,
  Briefcase,
  MessageSquare,
} from 'lucide-react'

interface DashboardStats {
  projects: number
  blogPosts: number
  services: number
  testimonials: number
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    projects: 0,
    blogPosts: 0,
    services: 0,
    testimonials: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const [projectsRes, blogRes, servicesRes, testimonialsRes] = await Promise.all([
          fetch('/api/projects'),
          fetch('/api/blog'),
          fetch('/api/services'),
          fetch('/api/testimonials'),
        ])

        const [projects, blogPosts, services, testimonials] = await Promise.all([
          projectsRes.json(),
          blogRes.json(),
          servicesRes.json(),
          testimonialsRes.json(),
        ])

        setStats({
          projects: projects.length || 0,
          blogPosts: blogPosts.length || 0,
          services: services.length || 0,
          testimonials: testimonials.length || 0,
        })
      } catch (error) {
        console.error('Error fetching stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const statCards = [
    {
      title: 'Projects',
      value: stats.projects,
      description: 'Total portfolio projects',
      icon: FolderOpen,
      color: 'text-blue-600',
    },
    {
      title: 'Blog Posts',
      value: stats.blogPosts,
      description: 'Published articles',
      icon: FileText,
      color: 'text-green-600',
    },
    {
      title: 'Services',
      value: stats.services,
      description: 'Available services',
      icon: Briefcase,
      color: 'text-purple-600',
    },
    {
      title: 'Testimonials',
      value: stats.testimonials,
      description: 'Client reviews',
      icon: MessageSquare,
      color: 'text-orange-600',
    },
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome to your portfolio content management system
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {statCards.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {loading ? '...' : stat.value}
                </div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common tasks to manage your portfolio content
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center justify-between">
                <span>Add New Project</span>
                <Badge variant="secondary">Create</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>Write Blog Post</span>
                <Badge variant="secondary">Write</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>Update Services</span>
                <Badge variant="secondary">Edit</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>Manage Testimonials</span>
                <Badge variant="secondary">Review</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest changes to your portfolio content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  Database initialized successfully
                </div>
                <div className="flex items-center text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                  Sample data seeded
                </div>
                <div className="flex items-center text-sm">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                  CMS dashboard ready
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
