import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { withCors } from '@/lib/cors'
import type { Session } from 'next-auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const education = await prisma.education.findUnique({
      where: { id },
    })

    if (!education) {
      return withCors(NextResponse.json(
        { error: 'Education record not found' },
        { status: 404 }
      ))
    }

    return withCors(NextResponse.json(education))
  } catch (error) {
    console.error('Error fetching education:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to fetch education' },
      { status: 500 }
    ))
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions) as Session | null

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()

    const education = await prisma.education.update({
      where: { id },
      data: body,
    })

    return withCors(NextResponse.json(education))
  } catch (error) {
    console.error('Error updating education:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to update education' },
      { status: 500 }
    ))
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions) as Session | null

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    
    await prisma.education.delete({
      where: { id },
    })

    return withCors(NextResponse.json({ success: true }))
  } catch (error) {
    console.error('Error deleting education:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to delete education' },
      { status: 500 }
    ))
  }
}
