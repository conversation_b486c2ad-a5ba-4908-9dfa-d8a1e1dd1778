import { NextResponse } from 'next/server'

const ALLOWED_ORIGINS = [
  'http://localhost:3000',
  'http://localhost:3001',
  'http://localhost:3002',
  'http://localhost:3003',
  'http://127.0.0.1:3000',
  'http://127.0.0.1:3001',
  'http://127.0.0.1:3002',
  'http://127.0.0.1:3003',
]

// Add production URLs from environment variables
if (process.env.PORTFOLIO_URL) {
  ALLOWED_ORIGINS.push(process.env.PORTFOLIO_URL)
}

if (process.env.NEXTAUTH_URL) {
  ALLOWED_ORIGINS.push(process.env.NEXTAUTH_URL)
}

export function corsHeaders(origin?: string) {
  const allowedOrigin = origin && ALLOWED_ORIGINS.includes(origin) ? origin : '*'

  return {
    'Access-Control-Allow-Origin': allowedOrigin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400',
  }
}

export function withCors(response: NextResponse, origin?: string) {
  const headers = corsHeaders(origin)
  Object.entries(headers).forEach(([key, value]) => {
    response.headers.set(key, value)
  })
  return response
}

export function handleOptions(origin?: string) {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders(origin),
  })
}

