import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { withCors } from '@/lib/cors'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const certification = await prisma.certification.findUnique({
      where: { id },
    })

    if (!certification) {
      return withCors(NextResponse.json(
        { error: 'Certification not found' },
        { status: 404 }
      ))
    }

    return withCors(NextResponse.json(certification))
  } catch (error) {
    console.error('Error fetching certification:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to fetch certification' },
      { status: 500 }
    ))
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()

    const certification = await prisma.certification.update({
      where: { id },
      data: body,
    })

    return withCors(NextResponse.json(certification))
  } catch (error) {
    console.error('Error updating certification:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to update certification' },
      { status: 500 }
    ))
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    
    await prisma.certification.delete({
      where: { id },
    })

    return withCors(NextResponse.json({ success: true }))
  } catch (error) {
    console.error('Error deleting certification:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to delete certification' },
      { status: 500 }
    ))
  }
}
