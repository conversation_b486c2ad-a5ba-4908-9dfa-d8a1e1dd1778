// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      Role     @default(ADMIN)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  projects     Project[]
  blogPosts    BlogPost[]
  testimonials Testimonial[]

  @@map("users")
}

enum Role {
  ADMIN
  EDITOR
}

// Project model
model Project {
  id              String   @id @default(cuid())
  title           String
  description     String
  longDescription String?
  image           String?
  category        String
  technologies    String[] // Array of technology names
  liveUrl         String?
  githubUrl       String?
  featured        <PERSON>olean  @default(false)
  published       Boolean  @default(true)
  order           Int      @default(0)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  authorId String
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@map("projects")
}

// Blog post model
model BlogPost {
  id          String   @id @default(cuid())
  title       String
  slug        String   @unique
  excerpt     String
  content     String
  image       String?
  category    String
  tags        String[] // Array of tag names
  published   Boolean  @default(false)
  featured    Boolean  @default(false)
  readTime    Int?     // Reading time in minutes
  views       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  publishedAt DateTime?

  // Relations
  authorId String
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@map("blog_posts")
}

// Service model
model Service {
  id          String   @id @default(cuid())
  title       String
  description String
  features    String[] // Array of feature descriptions
  icon        String   // Icon name or class
  color       String   // Color class for styling
  bgColor     String   // Background color class
  order       Int      @default(0)
  published   Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("services")
}

// Technology stack model
model TechStack {
  id        String   @id @default(cuid())
  name      String
  logo      String   // Logo image URL
  color     String   // Color class for styling
  category  String   // frontend, backend, tools, etc.
  order     Int      @default(0)
  published Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("tech_stack")
}

// Testimonial model
model Testimonial {
  id        String   @id @default(cuid())
  name      String
  role      String
  company   String
  content   String
  avatar    String?
  rating    Int      @default(5)
  featured  Boolean  @default(false)
  published Boolean  @default(true)
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  authorId String
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@map("testimonials")
}

// Work Experience model
model Experience {
  id           String   @id @default(cuid())
  title        String
  company      String
  companyLogo  String?  // URL to company logo image
  location     String
  period       String   // e.g., "2022 - Present"
  type         String   // e.g., "Full-time", "Part-time", "Contract"
  description  String
  achievements String[] // Array of achievement descriptions
  technologies String[] // Array of technology names
  website      String?  // Company website URL
  order        Int      @default(0)
  published    Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("experiences")
}

// Education model
model Education {
  id          String   @id @default(cuid())
  degree      String
  institution String
  location    String
  period      String   // e.g., "2020 - 2024"
  grade       String?  // e.g., "First Class with Distinction (8.5/10 CGPA)"
  description String
  highlights  String[] // Array of highlight descriptions
  order       Int      @default(0)
  published   Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("education")
}

// Certification model
model Certification {
  id           String   @id @default(cuid())
  title        String
  issuer       String
  date         String   // e.g., "2023"
  credentialId String?
  emoji        String?  // Emoji representation
  description  String
  order        Int      @default(0)
  published    Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("certifications")
}

// Analytics model for storing Google Analytics data
model Analytics {
  id          String   @id @default(cuid())
  date        DateTime
  pageViews   Int      @default(0)
  sessions    Int      @default(0)
  users       Int      @default(0)
  bounceRate  Float    @default(0)
  avgDuration Float    @default(0)
  topPages    Json?    // Store top pages data as JSON
  topSources  Json?    // Store traffic sources as JSON
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([date])
  @@map("analytics")
}
