# Database (Required) - Same as portfolio frontend
DATABASE_URL="postgresql://username:password@localhost:5432/portfolio_db"
# For production: DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"

# NextAuth.js (Required)
NEXTAUTH_URL="http://localhost:3000"
# For production: NEXTAUTH_URL="https://cms.ashishkamat.com.np"
NEXTAUTH_SECRET="your-secret-key-here-minimum-32-characters-long"

# Admin Credentials (for initial setup)
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your-secure-admin-password"

# Cloudinary (Required for image uploads)
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# Google Analytics (Optional)
GOOGLE_ANALYTICS_PROPERTY_ID="your-ga-property-id"
GOOGLE_SERVICE_ACCOUNT_EMAIL="<EMAIL>"
GOOGLE_PRIVATE_KEY="your-private-key"

# Portfolio Frontend URL (for API calls)
PORTFOLIO_URL="http://localhost:3001"
# For production: PORTFOLIO_URL="https://ashishkamat.com.np"

# Email Service (Optional - for notifications)
RESEND_API_KEY="your-resend-api-key"
