import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import type { Session } from 'next-auth'
import { prisma } from '@/lib/prisma'
import { withCors, handleOptions } from '@/lib/cors'

export async function GET(request: NextRequest) {
  try {
    const origin = request.headers.get('origin')

    const blogPosts = await prisma.blogPost.findMany({
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return withCors(NextResponse.json(blogPosts), origin || undefined)
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    const origin = request.headers.get('origin')
    return withCors(NextResponse.json(
      { error: 'Failed to fetch blog posts' },
      { status: 500 }
    ), origin || undefined)
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as Session | null

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      title,
      slug: providedSlug,
      excerpt,
      content,
      image,
      category,
      tags,
      published,
      featured,
      readTime: providedReadTime,
    } = body

    // Simple slug generation
    let finalSlug = providedSlug || title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')

    // Check for slug uniqueness
    const existingPost = await prisma.blogPost.findUnique({
      where: { slug: finalSlug }
    })

    if (existingPost) {
      // Append timestamp to make it unique
      finalSlug = `${finalSlug}-${Date.now()}`
    }

    // Simple reading time calculation (250 words per minute)
    let finalReadTime = providedReadTime
    if (!finalReadTime || finalReadTime <= 0) {
      const wordCount = content.split(/\s+/).length
      finalReadTime = Math.ceil(wordCount / 250)
    }

    const blogPost = await prisma.blogPost.create({
      data: {
        title,
        slug: finalSlug,
        excerpt,
        content,
        image,
        category,
        tags,
        published: published || false,
        featured: featured || false,
        readTime: finalReadTime,
        publishedAt: published ? new Date() : null,
        authorId: session.user.id,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    return withCors(NextResponse.json(blogPost, { status: 201 }))
  } catch (error) {
    console.error('Error creating blog post:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to create blog post' },
      { status: 500 }
    ))
  }
}

export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get('origin')
  return handleOptions(origin || undefined)
}
