import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { withCors } from '@/lib/cors'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const experience = await prisma.experience.findUnique({
      where: { id },
    })

    if (!experience) {
      return withCors(NextResponse.json(
        { error: 'Experience not found' },
        { status: 404 }
      ))
    }

    return withCors(NextResponse.json(experience))
  } catch (error) {
    console.error('Error fetching experience:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to fetch experience' },
      { status: 500 }
    ))
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()

    const experience = await prisma.experience.update({
      where: { id },
      data: body,
    })

    return withCors(NextResponse.json(experience))
  } catch (error) {
    console.error('Error updating experience:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to update experience' },
      { status: 500 }
    ))
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    
    await prisma.experience.delete({
      where: { id },
    })

    return withCors(NextResponse.json({ success: true }))
  } catch (error) {
    console.error('Error deleting experience:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to delete experience' },
      { status: 500 }
    ))
  }
}
