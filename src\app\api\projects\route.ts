import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { withCors, handleOptions } from '@/lib/cors'

export async function GET() {
  try {
    const projects = await prisma.project.findMany({
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        order: 'asc',
      },
    })

    return withCors(NextResponse.json(projects))
  } catch (error) {
    console.error('Error fetching projects:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    ))
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      title,
      description,
      longDescription,
      image,
      category,
      technologies,
      liveUrl,
      githubUrl,
      featured,
      published,
      order,
    } = body

    const project = await prisma.project.create({
      data: {
        title,
        description,
        longDescription,
        image,
        category,
        technologies,
        liveUrl,
        githubUrl,
        featured: featured || false,
        published: published !== undefined ? published : true,
        order: order || 0,
        authorId: session.user.id,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    return withCors(NextResponse.json(project, { status: 201 }))
  } catch (error) {
    console.error('Error creating project:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    ))
  }
}

export async function OPTIONS() {
  return handleOptions()
}
