'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/dashboard/layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, GraduationCap, MapPin, Calendar } from 'lucide-react'
import { toast } from 'sonner'

interface Education {
  id: string
  degree: string
  institution: string
  location: string
  period: string
  grade?: string
  description: string
  highlights: string[]
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export default function EducationPage() {
  const router = useRouter()
  const [education, setEducation] = useState<Education[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchEducation()
  }, [])

  const fetchEducation = async () => {
    try {
      const response = await fetch('/api/education')
      if (!response.ok) throw new Error('Failed to fetch education')
      const data = await response.json()
      setEducation(data)
    } catch (error) {
      console.error('Error fetching education:', error)
      toast.error('Failed to fetch education')
    } finally {
      setLoading(false)
    }
  }

  const deleteEducation = async (id: string) => {
    // Show confirmation toast with action buttons
    toast('Are you sure you want to delete this education record?', {
      action: {
        label: 'Delete',
        onClick: async () => {
          try {
            const response = await fetch(`/api/education/${id}`, {
              method: 'DELETE',
            })

            if (!response.ok) throw new Error('Failed to delete education')

            setEducation(education.filter(edu => edu.id !== id))
            toast.success('Education record deleted successfully')
          } catch (error) {
            console.error('Error deleting education:', error)
            toast.error('Failed to delete education')
          }
        }
      },
      cancel: {
        label: 'Cancel',
        onClick: () => {}
      }
    })
  }

  const togglePublished = async (id: string, published: boolean) => {
    try {
      const response = await fetch(`/api/education/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ published: !published }),
      })
      
      if (!response.ok) throw new Error('Failed to update education')
      
      setEducation(education.map(edu => 
        edu.id === id ? { ...edu, published: !published } : edu
      ))
      toast.success(`Education ${!published ? 'published' : 'unpublished'}`)
    } catch (error) {
      console.error('Error updating education:', error)
      toast.error('Failed to update education')
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-300 rounded w-64"></div>
            <div className="grid gap-4">
              {[1, 2].map((i) => (
                <div key={i} className="h-48 bg-gray-300 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Education</h1>
          <p className="text-muted-foreground">Manage your educational background</p>
        </div>
        <Button onClick={() => router.push('/dashboard/education/add')}>
          <Plus className="h-4 w-4 mr-2" />
          Add Education
        </Button>
      </div>

      <div className="grid gap-6">
        {education.map((edu) => (
          <Card key={edu.id} className="overflow-hidden">
            <CardHeader className="pb-4">
              <div className="flex justify-between items-start">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <GraduationCap className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">{edu.degree}</CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                      <div className="flex items-center">
                        <GraduationCap className="h-4 w-4 mr-1" />
                        {edu.institution}
                      </div>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {edu.location}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {edu.period}
                      </div>
                    </div>
                    {edu.grade && (
                      <div className="text-sm text-primary font-medium mt-1">
                        Grade: {edu.grade}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={edu.published ? "default" : "secondary"}>
                    {edu.published ? "Published" : "Draft"}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => togglePublished(edu.id, edu.published)}
                  >
                    {edu.published ? "Unpublish" : "Publish"}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push(`/dashboard/education/${edu.id}`)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteEducation(edu.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">{edu.description}</p>
              
              <div>
                <h4 className="font-medium mb-2">Key Highlights</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  {edu.highlights.map((highlight, index) => (
                    <li key={index}>{highlight}</li>
                  ))}
                </ul>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {education.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No education records found. Add your first education record to get started.</p>
          </CardContent>
        </Card>
      )}
      </div>
    </DashboardLayout>
  )
}
