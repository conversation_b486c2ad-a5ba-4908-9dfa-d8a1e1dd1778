import { withAuth } from 'next-auth/middleware'

export default withAuth(
  function middleware() {
    // Add any additional middleware logic here
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Protect dashboard routes
        if (req.nextUrl.pathname.startsWith('/dashboard')) {
          return !!token
        }

        // Allow public access to API routes for portfolio website
        if (req.nextUrl.pathname.startsWith('/api') && !req.nextUrl.pathname.startsWith('/api/auth')) {
          // Check if request is from portfolio website or has CORS headers
          const origin = req.headers.get('origin')
          const referer = req.headers.get('referer')


          // Allow public access to blog posts by slug (for portfolio website)
          const blogSlugPattern = /^\/api\/blog\/[^\/]+$/
          if (blogSlugPattern.test(req.nextUrl.pathname)) {
            // Check if this looks like a slug (not a CUID)
            const pathParts = req.nextUrl.pathname.split('/')
            const identifier = pathParts[pathParts.length - 1]
            const isSlug = !identifier.startsWith('c') || identifier.length <= 20

            if (isSlug) {
              return true // Allow public access for slug-based requests
            }
          }

          // Allow requests from portfolio website
          if (origin === 'http://localhost:3000' ||
              referer?.startsWith('http://localhost:3000') ||
              req.method === 'OPTIONS') {
            return true
          }

          // Require authentication for CMS dashboard API access
          return !!token
        }
        return true
      },
    },
  }
)

export const config = {
  matcher: ['/dashboard/:path*', '/api/:path*']
}
