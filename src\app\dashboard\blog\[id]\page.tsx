import { DashboardLayout } from '@/components/dashboard/layout'
import { BlogForm } from '@/components/forms/blog-form'

interface EditBlogPostPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function EditBlogPostPage({ params }: EditBlogPostPageProps) {
  const { id } = await params
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Blog Post</h1>
          <p className="text-muted-foreground">
            Update your blog post content
          </p>
        </div>

        <BlogForm postId={id} />
      </div>
    </DashboardLayout>
  )
}
