# Portfolio CMS - Vercel Deployment Guide

## Overview
This is the admin dashboard for managing your portfolio content. It should be deployed separately from your main portfolio for security.

## Prerequisites
1. GitHub repository with CMS code
2. Vercel account
3. **Same database** as your portfolio frontend
4. Cloudinary account (for image uploads)

## Environment Variables for Vercel

### Required Environment Variables
Set these in your Vercel dashboard under Project Settings > Environment Variables:

```bash
# Database (Same as portfolio frontend)
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"

# NextAuth.js
NEXTAUTH_URL="https://cms-yourname.vercel.app"
NEXTAUTH_SECRET="your-super-secret-key-min-32-chars"

# Admin Credentials
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your-secure-admin-password"

# Cloudinary (Required for image uploads)
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# Portfolio Frontend URL
PORTFOLIO_URL="https://yourname.vercel.app"
```

### Optional Environment Variables
```bash
# Google Analytics
GOOGLE_ANALYTICS_PROPERTY_ID="your-ga-property-id"
GOOGLE_SERVICE_ACCOUNT_EMAIL="<EMAIL>"
GOOGLE_PRIVATE_KEY="your-private-key"

# Email Service
RESEND_API_KEY="your-resend-api-key"
```

## Deployment Steps

### 1. Database Setup
- **Use the SAME database** as your portfolio frontend
- The CMS will automatically run migrations on deployment
- Seed data will be created for initial content

### 2. Cloudinary Setup (Required)
1. Go to [cloudinary.com](https://cloudinary.com)
2. Create free account
3. Get your credentials from Dashboard
4. Add to Vercel environment variables

### 3. Deploy CMS to Vercel
1. Go to [vercel.com](https://vercel.com)
2. Import your CMS repository (separate from portfolio)
3. Configure environment variables
4. Deploy!

### 4. Security Setup
1. **Change default admin password** immediately after first login
2. Consider adding IP restrictions in Vercel
3. Use strong passwords for admin accounts
4. Enable 2FA if available

### 5. Connect to Portfolio
- The CMS manages content in the shared database
- Your portfolio frontend reads from the same database
- Changes in CMS appear immediately on portfolio

## Build Configuration
- Build Command: `npm run vercel-build`
- Output Directory: `.next`
- Install Command: `npm install`
- Development Command: `npm run dev`

## Features
- ✅ Project management
- ✅ Blog post creation with rich text editor
- ✅ Experience & education management
- ✅ Technology stack management
- ✅ Testimonials management
- ✅ Image uploads via Cloudinary
- ✅ Analytics dashboard
- ✅ User authentication

## Recommended URLs
- Portfolio: `https://yourname.vercel.app`
- CMS: `https://cms-yourname.vercel.app` or `https://admin-yourname.vercel.app`

## Troubleshooting
1. **Database Connection**: Ensure DATABASE_URL is identical to portfolio
2. **Image Uploads**: Verify Cloudinary credentials
3. **Authentication**: Check NEXTAUTH_URL and NEXTAUTH_SECRET
4. **Build Errors**: Check build logs in Vercel dashboard

## Security Best Practices
- Never expose CMS URL publicly
- Use strong admin passwords
- Regularly update dependencies
- Monitor access logs
- Consider VPN access for extra security
